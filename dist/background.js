import { U as UserTabsRealTimeMonitor, a as WorkspaceSwitcher, _ as __vitePreload, d as WorkonaTabManager, C as COMMANDS, T as TabManager, e as WorkspaceSessionManager, W as WorkspaceManager, S as StorageManager, f as STORAGE_KEYS, M as MigrationManager } from './assets/dataMigration-CT_6i5DD.js';

if (typeof window === "undefined") {
  globalThis.window = {
    dispatchEvent: (event) => {
      console.log("Service Worker: 忽略 window.dispatchEvent 调用:", event.type);
      return true;
    }
  };
}
class BackgroundService {
  // 🛡️ Service Worker生命周期管理
  static isFirstRealStartup = true;
  static lastInitTimestamp = 0;
  static SERVICE_WORKER_WAKEUP_THRESHOLD = 5 * 60 * 1e3;
  // 5分钟
  constructor() {
    this.init();
  }
  /**
   * 初始化后台服务
   */
  async init() {
    const initStartTime = Date.now();
    console.log("🚀 BackgroundService 初始化开始");
    await this.setupSidePanel();
    this.setupCommandListeners();
    this.setupTabListeners();
    this.setupStorageListeners();
    await this.checkAndMigrateData();
    await this.smartWorkspaceStateManagement();
    await this.restoreTabMappingsAfterRestart();
    await this.initializeDefaultData();
    await this.startUserTabsRealTimeMonitoring();
    const initDuration = Date.now() - initStartTime;
    console.log(`✅ BackgroundService 初始化完成 (耗时: ${initDuration}ms)`);
    this.logServiceWorkerLifecycle();
    this.setupServiceWorkerLifecycleMonitoring();
  }
  /**
   * 📊 记录Service Worker生命周期信息
   */
  logServiceWorkerLifecycle() {
    const now = Date.now();
    const timeSinceLastInit = now - BackgroundService.lastInitTimestamp;
    console.log("📊 Service Worker生命周期信息:", {
      isFirstRealStartup: BackgroundService.isFirstRealStartup,
      lastInitTimestamp: new Date(BackgroundService.lastInitTimestamp).toISOString(),
      timeSinceLastInit: `${Math.round(timeSinceLastInit / 1e3)}秒`,
      currentTimestamp: new Date(now).toISOString(),
      wakeupThreshold: `${BackgroundService.SERVICE_WORKER_WAKEUP_THRESHOLD / 1e3}秒`
    });
  }
  /**
   * 🔄 设置Service Worker生命周期监控
   */
  setupServiceWorkerLifecycleMonitoring() {
    if (chrome.runtime.onStartup) {
      chrome.runtime.onStartup.addListener(async () => {
        console.log("🚀 Chrome扩展启动事件触发 - 浏览器重启");
        BackgroundService.isFirstRealStartup = true;
        BackgroundService.lastInitTimestamp = 0;
        await this.resetWorkspaceStateOnBrowserRestart();
      });
    }
    if (chrome.runtime.onInstalled) {
      chrome.runtime.onInstalled.addListener(async (details) => {
        console.log("📦 Chrome扩展安装/更新事件:", details.reason);
        BackgroundService.isFirstRealStartup = true;
        BackgroundService.lastInitTimestamp = 0;
        if (details.reason === "install") {
          console.log("🎉 扩展首次安装");
          await this.resetWorkspaceStateOnBrowserRestart();
        } else if (details.reason === "update") {
          console.log("🔄 扩展更新");
        }
      });
    }
    if (chrome.runtime.onSuspend) {
      chrome.runtime.onSuspend.addListener(() => {
        console.log("😴 Service Worker即将挂起");
      });
    }
    if (chrome.runtime.onSuspendCanceled) {
      chrome.runtime.onSuspendCanceled.addListener(() => {
        console.log("🔄 Service Worker挂起被取消");
      });
    }
  }
  /**
   * 启动用户标签页实时监控
   */
  async startUserTabsRealTimeMonitoring() {
    try {
      UserTabsRealTimeMonitor.startMonitoring();
      console.log("📊 用户标签页实时监控已启动");
    } catch (error) {
      console.warn("启动用户标签页实时监控失败:", error);
    }
  }
  /**
   * 刷新用户标签页实时监控
   */
  async refreshUserTabsMonitoring() {
    try {
      let activeWorkspace = null;
      const currentWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
        activeWorkspace = currentWorkspaceResult.data;
        console.log(`🎯 使用当前存储的工作区进行监控刷新: ${activeWorkspace.name}`);
      } else {
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          activeWorkspace = activeWorkspaceResult.data;
          console.log(`🔍 通过检测获取工作区进行监控刷新: ${activeWorkspace.name}`);
        }
      }
      if (activeWorkspace) {
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(activeWorkspace.id);
      } else {
        console.log("⚠️ 没有活跃工作区，跳过用户标签页监控刷新");
      }
    } catch (error) {
      console.warn("刷新用户标签页实时监控失败:", error);
    }
  }
  /**
   * 同步标签页编辑后的状态
   */
  async syncTabAfterEdit(tabId, changeInfo, _tab) {
    try {
      if (changeInfo.url || changeInfo.title) {
        const { WorkonaTabManager: WorkonaTabManager2 } = await __vitePreload(async () => { const { WorkonaTabManager: WorkonaTabManager2 } = await import('./assets/dataMigration-CT_6i5DD.js').then(n => n.i);return { WorkonaTabManager: WorkonaTabManager2 }},true?[]:void 0);
        await WorkonaTabManager2.syncTabAfterEdit(tabId, changeInfo.url, changeInfo.title);
      }
    } catch (error) {
      console.warn("同步标签页编辑状态失败:", error);
    }
  }
  /**
   * 处理标签页固定状态变化
   */
  async handleTabPinnedStateChange(tabId, isPinned, tab) {
    try {
      console.log(`📌 标签页固定状态变化: ${tabId} -> ${isPinned ? "固定" : "取消固定"}`);
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        console.log(`⚠️ 标签页 ${tabId} 没有 Workona ID 映射，跳过固定状态同步`);
        return;
      }
      const workonaId = workonaIdResult.data;
      const workspaceId = workonaId.split("-")[1];
      await WorkonaTabManager.updateTabMetadata(workonaId, {
        metadata: {
          isPinned,
          pinnedAt: isPinned ? Date.now() : void 0,
          unpinnedAt: !isPinned ? Date.now() : void 0
        }
      });
      if (isPinned) {
        await this.updateWorkspacePinnedTabIds(workspaceId, tabId);
      } else {
        await this.removeFromWorkspacePinnedTabIds(workspaceId, tabId);
      }
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          func: (isPinned2) => {
            const existingData = sessionStorage.getItem("workonaData");
            if (existingData) {
              const workonaData = JSON.parse(existingData);
              workonaData.isPinned = isPinned2;
              workonaData.timestamp = Date.now();
              sessionStorage.setItem("workonaData", JSON.stringify(workonaData));
              console.log(`📝 更新标签页会话存储的固定状态: ${isPinned2}`);
            }
          },
          args: [isPinned]
        });
      } catch (error) {
        console.warn(`⚠️ 更新标签页 ${tabId} 会话存储失败:`, error);
      }
      console.log(`✅ 已同步标签页 ${tabId} 的固定状态: ${isPinned ? "固定" : "取消固定"}`);
    } catch (error) {
      console.warn("处理标签页固定状态变化失败:", error);
    }
  }
  /**
   * 设置侧边栏
   */
  async setupSidePanel() {
    try {
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error("Failed to setup side panel:", error);
    }
  }
  /**
   * 设置命令监听器
   */
  setupCommandListeners() {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log("Command received:", command);
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          case "test_auto_classify":
            await this.testAutoClassify();
            break;
          default:
            console.log("Unknown command:", command);
        }
      } catch (error) {
        console.error("Error handling command:", command, error);
      }
    });
  }
  /**
   * 测试自动分类功能
   */
  async testAutoClassify() {
    try {
      console.log("🧪 [测试] 开始测试自动分类功能...");
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        console.log("❌ [测试] 没有找到活跃标签页");
        return;
      }
      const activeTab = tabs[0];
      console.log("🧪 [测试] 当前活跃标签页:", {
        id: activeTab.id,
        url: activeTab.url,
        title: activeTab.title
      });
      if (!activeTab.id || !activeTab.url) {
        console.log("❌ [测试] 活跃标签页缺少ID或URL");
        return;
      }
      console.log("🧪 [测试] 手动触发自动分类...");
      const result = await TabManager.autoClassifyNewTab(activeTab.id, activeTab.url);
      console.log("🧪 [测试] 自动分类结果:", result);
      const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      console.log("🧪 [测试] 验证 Workona ID:", verifyResult);
    } catch (error) {
      console.error("❌ [测试] 测试自动分类功能失败:", error);
    }
  }
  /**
   * 设置标签页监听器
   */
  setupTabListeners() {
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        console.log("标签页激活，记录状态变化");
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeInfo.tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          await WorkspaceSessionManager.setActiveTab(workonaIdResult.data);
        }
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      } catch (error) {
        console.error("Error handling tab activation:", error);
      }
    });
    chrome.tabs.onMoved.addListener(async (_tabId, _moveInfo) => {
      try {
        console.log("标签页位置变化，同步工作区状态");
        setTimeout(async () => {
          await WorkspaceSessionManager.syncCurrentWorkspaceState();
        }, 100);
      } catch (error) {
        console.error("Error handling tab move:", error);
      }
    });
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log("🆕 [LISTENER] 标签页创建事件触发:", {
          id: tab.id,
          url: tab.url || "(no URL yet)",
          title: tab.title || "(no title yet)",
          status: tab.status,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        });
        if (tab.id && tab.url && !tab.url.includes("chrome://") && !tab.url.includes("chrome-extension://") && !tab.url.includes("about:") && tab.url !== "chrome://newtab/") {
          console.log(`🎯 [LISTENER] 标签页创建时尝试自动分类: ID=${tab.id}, URL=${tab.url}`);
          try {
            console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
            const result = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
            console.log(`✨ [LISTENER] 已自动分类新标签页: ${tab.url}`);
          } catch (error) {
            console.error("❌ [LISTENER] 自动分类新标签页失败:", error);
          }
        } else {
          console.log(`⏭️ 跳过标签页创建时的自动分类: ID=${tab.id}, URL=${tab.url}, 原因: ${!tab.id ? "无ID" : !tab.url ? "无URL" : tab.url.includes("chrome://") ? "系统页面" : tab.url.includes("chrome-extension://") ? "扩展页面" : tab.url.includes("about:") ? "about页面" : tab.url === "chrome://newtab/" ? "新标签页" : "未知原因"}`);
        }
        await this.notifyGlobalUserTabsStateChange("tab_created");
        const { UserTabsRealTimeMonitor: UserTabsRealTimeMonitor2 } = await __vitePreload(async () => { const { UserTabsRealTimeMonitor: UserTabsRealTimeMonitor2 } = await import('./assets/dataMigration-CT_6i5DD.js').then(n => n.t);return { UserTabsRealTimeMonitor: UserTabsRealTimeMonitor2 }},true?[]:void 0);
        await UserTabsRealTimeMonitor2.triggerImmediateStateCheck();
      } catch (error) {
        console.error("Error handling tab creation:", error);
      }
    });
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      try {
        if ((changeInfo.url || changeInfo.status === "complete") && tab.url) {
          console.log("🔄 [LISTENER] 标签页更新事件触发:", {
            id: tabId,
            url: tab.url,
            title: tab.title,
            status: tab.status,
            changeInfo,
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          });
          const shouldClassify = tab.url && !tab.url.includes("chrome://") && !tab.url.includes("chrome-extension://") && !tab.url.includes("about:") && tab.url !== "chrome://newtab/" && tab.url !== "";
          if (shouldClassify) {
            console.log(`🎯 [LISTENER] 标签页更新时尝试自动分类: ID=${tabId}, URL=${tab.url}`);
            try {
              console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
              const result = await TabManager.autoClassifyNewTab(tabId, tab.url);
              console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
              console.log(`✨ [LISTENER] 已自动分类更新的标签页: ${tab.url}`);
            } catch (error) {
              console.error("❌ [LISTENER] 自动分类更新标签页失败:", error);
            }
          } else {
            console.log(`⏭️ 跳过标签页更新时的自动分类: ID=${tabId}, URL=${tab.url}, 原因: ${!tab.url ? "无URL" : tab.url.includes("chrome://") ? "系统页面" : tab.url.includes("chrome-extension://") ? "扩展页面" : tab.url.includes("about:") ? "about页面" : tab.url === "chrome://newtab/" ? "新标签页" : tab.url === "" ? "空URL" : "未知原因"}`);
          }
          await this.syncTabAfterEdit(tabId, changeInfo, tab);
          await this.notifyGlobalUserTabsStateChange("tab_updated");
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
        } else {
          console.log(`⏭️ 跳过标签页更新事件: ID=${tabId}, 原因: ${!changeInfo.url && changeInfo.status !== "complete" ? "非URL变化且非完成状态" : !tab.url ? "无URL" : "未知原因"}`);
        }
        if (changeInfo.pinned !== void 0) {
          await this.handleTabPinnedStateChange(tabId, changeInfo.pinned, tab);
        }
      } catch (error) {
        console.error("Error handling tab update:", error);
      }
    });
    chrome.tabs.onRemoved.addListener(async (tabId, _removeInfo) => {
      try {
        console.log("Tab removed:", tabId);
        try {
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
          if (workonaIdResult.success && workonaIdResult.data) {
            const workonaId = workonaIdResult.data;
            const workspaceId = workonaId.split("-")[1];
            await WorkspaceManager.removeWorkonaTabId(workspaceId, workonaId);
            await WorkonaTabManager.removeTabMapping(workonaId);
            console.log(`🗑️ 清理已删除标签页的 Workona ID: ${workonaId}`);
          }
        } catch (workonaError) {
          console.error("清理 Workona ID 映射失败:", workonaError);
        }
        await this.notifyGlobalUserTabsStateChange("tab_removed");
        const { UserTabsRealTimeMonitor: UserTabsRealTimeMonitor2 } = await __vitePreload(async () => { const { UserTabsRealTimeMonitor: UserTabsRealTimeMonitor2 } = await import('./assets/dataMigration-CT_6i5DD.js').then(n => n.t);return { UserTabsRealTimeMonitor: UserTabsRealTimeMonitor2 }},true?[]:void 0);
        await UserTabsRealTimeMonitor2.triggerImmediateStateCheck();
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error("Error handling tab removal:", error);
      }
    });
    chrome.tabs.onMoved.addListener(async (tabId, moveInfo) => {
      try {
        console.log("Tab moved:", tabId, moveInfo);
        await this.notifyGlobalUserTabsStateChange("tab_moved");
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error("Error handling tab move:", error);
      }
    });
    chrome.tabs.onAttached.addListener(async (tabId, attachInfo) => {
      try {
        console.log("Tab attached:", tabId, attachInfo);
        await this.notifyGlobalUserTabsStateChange("tab_attached");
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error("Error handling tab attach:", error);
      }
    });
    chrome.tabs.onDetached.addListener(async (tabId, detachInfo) => {
      try {
        console.log("Tab detached:", tabId, detachInfo);
        await this.notifyGlobalUserTabsStateChange("tab_detached");
      } catch (error) {
        console.error("Error handling tab detach:", error);
      }
    });
  }
  /**
   * 设置存储监听器
   */
  setupStorageListeners() {
    StorageManager.onChanged((changes) => {
      console.log("Storage changed:", changes);
      this.notifySidePanelUpdate(changes);
    });
  }
  /**
   * 恢复浏览器重启后的标签页映射关系（纯 Workona ID 血缘跟踪）
   */
  async restoreTabMappingsAfterRestart() {
    try {
      console.log("🔄 开始恢复浏览器重启后的标签页映射关系（纯 Workona ID 血缘跟踪）...");
      const allTabs = await chrome.tabs.query({});
      console.log(`📊 发现 ${allTabs.length} 个当前标签页`);
      await StorageManager.saveTabIdMappings([]);
      console.log("🗑️ 已清理旧的标签页映射");
      let restoredCount = 0;
      let userTabsCount = 0;
      for (const tab of allTabs) {
        if (!tab.id || !tab.url) continue;
        if (tab.url.includes("chrome://") || tab.url.includes("chrome-extension://") || tab.url.includes("about:")) {
          continue;
        }
        try {
          const results = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: () => {
              const workonaDataStr = sessionStorage.getItem("workonaData");
              if (workonaDataStr) {
                try {
                  return JSON.parse(workonaDataStr);
                } catch {
                  return null;
                }
              }
              return null;
            }
          });
          const workonaData = results[0]?.result;
          if (workonaData && workonaData.workonaId) {
            console.log(`🔗 通过会话存储恢复标签页映射: ${workonaData.workonaId} <-> ${tab.id}`);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              workonaData.workonaId,
              tab.id,
              workonaData.workspaceId,
              workonaData.websiteId || void 0,
              {
                isWorkspaceCore: workonaData.isWorkspaceCore,
                source: "session_restored"
              }
            );
            if (mappingResult.success) {
              await WorkonaTabManager.updateTabMetadata(workonaData.workonaId, {
                metadata: {
                  isPinned: false,
                  // 浏览器重启后重置固定状态
                  unpinnedAt: Date.now()
                }
              });
              console.log(`📌 重置固定状态: ${workonaData.workonaId} -> 浏览器重启后重置为非固定`);
            }
            if (mappingResult.success) {
              restoredCount++;
              console.log(`✅ 成功恢复标签页映射: ${workonaData.workonaId} <-> ${tab.id} (核心: ${workonaData.isWorkspaceCore})`);
              if (tab.pinned) {
                await this.updateWorkspacePinnedTabIds(workonaData.workspaceId, tab.id);
              }
            }
          } else {
            try {
              const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url);
              if (classifyResult.success) {
                userTabsCount++;
                console.log(`🆕 为新用户标签页创建映射: ${tab.title}`);
              }
            } catch (error) {
              console.warn(`⚠️ 为用户标签页创建映射失败: ${tab.title}`, error);
            }
          }
        } catch (error) {
          console.warn(`⚠️ 无法访问标签页 ${tab.id} 的会话存储:`, error);
          try {
            const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            if (classifyResult.success) {
              userTabsCount++;
              console.log(`🆕 为无法访问会话存储的标签页创建用户映射: ${tab.title}`);
            }
          } catch (classifyError) {
            console.warn(`⚠️ 为用户标签页创建映射失败: ${tab.title}`, classifyError);
          }
        }
      }
      console.log(`✅ 标签页映射恢复完成: 恢复 ${restoredCount} 个 Workona 标签页, 创建 ${userTabsCount} 个用户标签页映射`);
      await this.cleanupAllWorkspacePinnedStates();
    } catch (error) {
      console.error("❌ 恢复标签页映射失败:", error);
    }
  }
  /**
   * 更新工作区的固定标签页ID列表
   * 在浏览器重启后，Chrome ID 会变化，需要更新存储的固定状态
   */
  async updateWorkspacePinnedTabIds(workspaceId, newChromeId) {
    try {
      const storageKey = `workspacePinnedTabIds_${workspaceId}`;
      const result = await chrome.storage.local.get([storageKey]);
      let pinnedTabIds = result[storageKey] || [];
      const validPinnedTabIds = [];
      for (const tabId of pinnedTabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab && tab.pinned) {
            validPinnedTabIds.push(tabId);
          }
        } catch (error) {
          console.log(`🗑️ 清理无效的固定标签页 ID: ${tabId}`);
        }
      }
      if (!validPinnedTabIds.includes(newChromeId)) {
        validPinnedTabIds.push(newChromeId);
        console.log(`📌 更新工作区 ${workspaceId} 的固定状态存储，添加新 Chrome ID: ${newChromeId}`);
      }
      if (validPinnedTabIds.length !== pinnedTabIds.length || !validPinnedTabIds.every((id) => pinnedTabIds.includes(id))) {
        await chrome.storage.local.set({ [storageKey]: validPinnedTabIds });
        console.log(`💾 工作区 ${workspaceId} 固定状态存储已更新: ${validPinnedTabIds.length} 个有效标签页`);
      }
    } catch (error) {
      console.warn(`⚠️ 更新工作区固定状态存储失败:`, error);
    }
  }
  /**
   * 从工作区的固定标签页ID列表中移除指定的 Chrome ID
   */
  async removeFromWorkspacePinnedTabIds(workspaceId, chromeId) {
    try {
      const storageKey = `workspacePinnedTabIds_${workspaceId}`;
      const result = await chrome.storage.local.get([storageKey]);
      let pinnedTabIds = result[storageKey] || [];
      const originalLength = pinnedTabIds.length;
      pinnedTabIds = pinnedTabIds.filter((id) => id !== chromeId);
      if (pinnedTabIds.length !== originalLength) {
        if (pinnedTabIds.length === 0) {
          await chrome.storage.local.remove([storageKey]);
          console.log(`🗑️ 删除工作区 ${workspaceId} 的空固定状态存储`);
        } else {
          await chrome.storage.local.set({ [storageKey]: pinnedTabIds });
          console.log(`📌 从工作区 ${workspaceId} 的固定状态存储中移除 Chrome ID: ${chromeId}`);
        }
      }
    } catch (error) {
      console.warn(`⚠️ 从工作区固定状态存储中移除 Chrome ID 失败:`, error);
    }
  }
  /**
   * 清理所有工作区的固定状态存储
   * 移除无效的 Chrome ID，确保固定状态存储的准确性
   */
  async cleanupAllWorkspacePinnedStates() {
    try {
      console.log("🧹 开始清理所有工作区的固定状态存储...");
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.warn("⚠️ 获取工作区列表失败，跳过固定状态清理");
        return;
      }
      const workspaces = workspacesResult.data;
      let totalCleaned = 0;
      for (const workspace of workspaces) {
        const storageKey = `workspacePinnedTabIds_${workspace.id}`;
        const result = await chrome.storage.local.get([storageKey]);
        const pinnedTabIds = result[storageKey] || [];
        if (pinnedTabIds.length === 0) continue;
        const validPinnedTabIds = [];
        for (const tabId of pinnedTabIds) {
          try {
            const tab = await chrome.tabs.get(tabId);
            if (tab && tab.pinned) {
              validPinnedTabIds.push(tabId);
            } else {
              totalCleaned++;
              console.log(`🗑️ 清理工作区 "${workspace.name}" 的无效固定标签页 ID: ${tabId}`);
            }
          } catch (error) {
            totalCleaned++;
            console.log(`🗑️ 清理工作区 "${workspace.name}" 的不存在标签页 ID: ${tabId}`);
          }
        }
        if (validPinnedTabIds.length !== pinnedTabIds.length) {
          if (validPinnedTabIds.length === 0) {
            await chrome.storage.local.remove([storageKey]);
            console.log(`🗑️ 删除工作区 "${workspace.name}" 的空固定状态存储`);
          } else {
            await chrome.storage.local.set({ [storageKey]: validPinnedTabIds });
            console.log(`💾 更新工作区 "${workspace.name}" 的固定状态存储: ${validPinnedTabIds.length} 个有效标签页`);
          }
        }
      }
      console.log(`✅ 固定状态清理完成，共清理 ${totalCleaned} 个无效条目`);
    } catch (error) {
      console.warn("⚠️ 清理工作区固定状态存储失败:", error);
    }
  }
  /**
   * 🛡️ 智能工作区状态管理
   * 区分真正的启动和Service Worker唤醒
   * 浏览器重启时重置工作区选择状态（用户需求）
   */
  async smartWorkspaceStateManagement() {
    try {
      const now = Date.now();
      const timeSinceLastInit = now - BackgroundService.lastInitTimestamp;
      console.log(`🔍 工作区状态管理检查: 距离上次初始化 ${Math.round(timeSinceLastInit / 1e3)}秒`);
      if (timeSinceLastInit < BackgroundService.SERVICE_WORKER_WAKEUP_THRESHOLD && !BackgroundService.isFirstRealStartup) {
        console.log("🔄 检测到Service Worker唤醒，保持工作区状态");
        BackgroundService.lastInitTimestamp = now;
        return;
      }
      console.log("🔄 检测到真正的启动事件 - 浏览器重启或扩展首次加载");
      console.log("🔄 根据用户需求，重置工作区选择状态");
      await this.resetWorkspaceStateOnBrowserRestart();
      BackgroundService.isFirstRealStartup = false;
      BackgroundService.lastInitTimestamp = now;
    } catch (error) {
      console.error("❌ 智能工作区状态管理失败:", error);
      try {
        await this.resetWorkspaceStateOnBrowserRestart();
      } catch (resetError) {
        console.error("❌ 重置工作区状态失败:", resetError);
      }
    }
  }
  /**
   * 🔄 尝试恢复工作区状态
   */
  async attemptWorkspaceStateRecovery() {
    try {
      console.log("🔄 尝试恢复工作区状态...");
      const lastActiveResult = await StorageManager.getLastActiveWorkspaceIds();
      if (lastActiveResult.success && lastActiveResult.data && lastActiveResult.data.length > 0) {
        const lastWorkspaceId = lastActiveResult.data[0];
        const workspaceResult = await StorageManager.getWorkspace(lastWorkspaceId);
        if (workspaceResult.success) {
          console.log(`🔄 恢复最近使用的工作区: ${workspaceResult.data.name}`);
          await StorageManager.setActiveWorkspaceId(lastWorkspaceId);
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success && workspacesResult.data) {
            const workspaces = workspacesResult.data.map((workspace) => ({
              ...workspace,
              isActive: workspace.id === lastWorkspaceId
            }));
            await StorageManager.saveWorkspaces(workspaces);
          }
          return;
        }
      }
      console.log("⚠️ 无法恢复工作区状态，保持未选择状态");
    } catch (error) {
      console.error("❌ 工作区状态恢复失败:", error);
    }
  }
  /**
   * 🔄 浏览器重启后重置工作区选择状态
   * 确保用户在浏览器重启后需要手动选择工作区
   */
  async resetWorkspaceStateOnBrowserRestart() {
    try {
      console.log("🔄 浏览器重启检测 - 开始重置工作区选择状态");
      await StorageManager.setActiveWorkspaceId(null);
      console.log("✅ 已清除活跃工作区ID");
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data) {
        const workspaces = workspacesResult.data.map((workspace) => ({
          ...workspace,
          isActive: false
        }));
        await StorageManager.saveWorkspaces(workspaces);
        console.log("✅ 已重置所有工作区的激活状态");
      }
      await this.clearWorkspaceSessionState();
      await this.notifyWorkspaceStateReset();
      console.log("✅ 浏览器重启后工作区状态重置完成");
    } catch (error) {
      console.error("❌ 重置工作区状态失败:", error);
    }
  }
  /**
   * 清除工作区会话状态
   */
  async clearWorkspaceSessionState() {
    try {
      const { WorkspaceSessionManager: WorkspaceSessionManager2 } = await __vitePreload(async () => { const { WorkspaceSessionManager: WorkspaceSessionManager2 } = await import('./assets/dataMigration-CT_6i5DD.js').then(n => n.g);return { WorkspaceSessionManager: WorkspaceSessionManager2 }},true?[]:void 0);
      if (WorkspaceSessionManager2.clearCurrentSession) {
        await WorkspaceSessionManager2.clearCurrentSession();
        console.log("✅ 已清除工作区会话状态");
      }
    } catch (error) {
      console.warn("⚠️ 清除工作区会话状态失败:", error);
    }
  }
  /**
   * 通知侧边栏工作区状态已重置
   */
  async notifyWorkspaceStateReset() {
    try {
      chrome.runtime.sendMessage({
        type: "WORKSPACE_STATE_RESET",
        timestamp: Date.now()
      }).catch((error) => {
        console.log("发送工作区状态重置通知失败:", error);
      });
      const changes = {
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: { newValue: null, oldValue: void 0 }
      };
      this.notifySidePanelUpdate(changes);
      console.log("✅ 已通知侧边栏工作区状态重置");
    } catch (error) {
      console.warn("⚠️ 通知侧边栏状态重置失败:", error);
    }
  }
  /**
   * 清除活跃工作区状态（仅在必要时使用）
   * @deprecated 使用 resetWorkspaceStateOnBrowserRestart 替代
   */
  async clearActiveWorkspaceOnStartup() {
    try {
      console.log("🔄 清除活跃工作区状态，让用户手动选择");
      await StorageManager.setActiveWorkspaceId(null);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data) {
        const workspaces = workspacesResult.data.map((workspace) => ({
          ...workspace,
          isActive: false
        }));
        await StorageManager.saveWorkspaces(workspaces);
      }
      console.log("✅ 活跃工作区状态已清除");
    } catch (error) {
      console.error("❌ 清除活跃工作区状态失败:", error);
    }
  }
  /**
   * 初始化默认数据
   */
  async initializeDefaultData() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data.length === 0) {
        console.log("No workspaces found, creating default workspace templates");
      }
    } catch (error) {
      console.error("Error initializing default data:", error);
    }
  }
  /**
   * 根据索引切换工作区
   */
  async switchToWorkspaceByIndex(index) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error("Failed to get workspaces:", workspacesResult.error);
        return;
      }
      const workspaces = workspacesResult.data;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        const result = await WorkspaceSwitcher.switchToWorkspace(workspace.id);
        if (result.success) {
          console.log(`Switched to workspace: ${workspace.name}`);
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        } else {
          console.error("Failed to switch workspace:", result.error);
        }
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error("Error switching workspace by index:", error);
    }
  }
  /**
   * 切换侧边栏显示状态
   */
  async toggleSidePanel() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id;
        console.log("Toggle side panel for tab:", tabId);
      }
    } catch (error) {
      console.error("Error toggling side panel:", error);
    }
  }
  /**
   * 显示通知
   */
  showNotification(message, icon) {
    try {
      chrome.notifications.create({
        type: "basic",
        iconUrl: "icons/icon-48.png",
        title: "WorkSpace Pro",
        message: `${icon || "🚀"} ${message}`
      });
      console.log("Notification shown:", message);
    } catch (error) {
      console.error("Error showing notification:", error);
    }
  }
  /**
   * 通知侧边栏更新
   */
  notifySidePanelUpdate(_changes) {
    try {
      console.log("Notifying side panel of storage changes");
    } catch (error) {
      console.error("Error notifying side panel:", error);
    }
  }
  /**
   * 通知全局用户标签页状态变化
   */
  async notifyGlobalUserTabsStateChange(eventType) {
    try {
      const sendNotification = async () => {
        try {
          if (typeof chrome !== "undefined" && chrome.runtime) {
            chrome.runtime.sendMessage({
              type: "USER_TABS_VISIBILITY_CHANGED",
              workspaceId: "global",
              eventType
            }).catch((error) => {
              console.log("发送用户标签页状态变化消息失败:", error);
            });
          }
          console.log(`📢 已通知全局用户标签页状态变化: ${eventType}`);
        } catch (error) {
          console.error("发送全局用户标签页状态变化通知失败:", error);
        }
      };
      await sendNotification();
      setTimeout(sendNotification, 100);
    } catch (error) {
      console.error("通知全局用户标签页状态变化失败:", error);
    }
  }
  /**
   * 检查并执行数据迁移
   */
  async checkAndMigrateData() {
    try {
      console.log("🔍 检查数据迁移需求...");
      const versionResult = await MigrationManager.detectDataVersion();
      if (!versionResult.success) {
        console.error("检测数据版本失败:", versionResult.error);
        return;
      }
      const currentVersion = versionResult.data;
      console.log(`📊 当前数据版本: ${currentVersion}`);
      if (currentVersion !== "1.0.0") {
        console.log("🚀 开始执行数据迁移...");
        const migrationResult = await MigrationManager.migrateToWorkonaFormat({
          backupOriginalData: true,
          validateAfterMigration: true,
          rollbackOnError: true,
          preserveUserPreferences: true
        });
        if (migrationResult.success) {
          if (migrationResult.data) {
            console.log("✅ 基础数据迁移成功完成");
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          } else {
            console.log("ℹ️ 数据已是最新版本，无需迁移");
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          }
        } else {
          console.error("❌ 数据迁移失败:", migrationResult.error);
        }
      } else {
        console.log("✅ 数据版本已是最新，无需迁移");
      }
    } catch (error) {
      console.error("❌ 数据迁移检查过程中发生错误:", error);
    }
  }
}
new BackgroundService();
